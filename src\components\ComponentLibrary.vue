<template>
  <div class="component-library">
    <el-tabs v-model="activeTab" class="library-tabs">
      <el-tab-pane label="基础组件" name="basic">
        <div class="component-grid">
          <div 
            v-for="component in basicComponents" 
            :key="component.type"
            class="component-item"
            draggable="true"
            @dragstart="handleDragStart($event, component)"
          >
            <div class="component-icon">
              <component :is="component.icon" size="1.2em" stroke-width="1.3" />
            </div>
            <div class="component-name">{{ component.name }}</div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import WindowTitleBar from './WindowTitleBar.vue'
import {
  IconPhoto, IconTypography, IconInputAi, IconCashBanknote,
  IconSelector, IconCalendar, IconClock, IconToggleLeft,
  IconAdjustments, IconStar, IconPalette, IconArrowsExchange,
  IconCheckbox, IconCircleDot, IconTextSize, IconList,
  IconLayoutCards, IconPlayerPlay, IconTag, IconTableRow, IconTable
} from '@tabler/icons-vue'

interface ComponentInfo {
  type: string
  name: string
  icon: any
  defaultProps: Record<string, any>
  defaultStyle: Record<string, any>
}

const activeTab = ref('basic')

const basicComponents: ComponentInfo[] = [
  {
    type: 'button',
    name: '按钮',
    icon: IconCashBanknote,
    defaultProps: { text: '按钮', type: 'primary' },
    defaultStyle: { fontSize: '14px' }
  },
  {
    type: 'text',
    name: '文本',
    icon: IconTypography,
    defaultProps: { content: '这是一段文本' },
    defaultStyle: { fontSize: '14px', color: '#333333' }
  },
  {
    type: 'input',
    name: '输入框',
    icon: IconInputAi,
    defaultProps: { placeholder: '请输入内容' },
    defaultStyle: { width: '200px' }
  },
  {
    type: 'image',
    name: '图片',
    icon: IconPhoto,
    defaultProps: { src: 'https://via.placeholder.com/150', alt: '图片' },
    defaultStyle: { width: '120px', height: '80px' }
  },
  {
    type: 'select',
    name: '选择器',
    icon: IconSelector,
    defaultProps: {
      placeholder: '请选择',
      options: [
        { label: '选项1', value: 'option1' },
        { label: '选项2', value: 'option2' },
        { label: '选项3', value: 'option3' }
      ]
    },
    defaultStyle: { width: '200px' }
  },
  {
    type: 'textarea',
    name: '多行文本',
    icon: IconTextSize,
    defaultProps: {
      placeholder: '请输入内容',
      rows: 3
    },
    defaultStyle: { width: '200px' }
  },
  {
    type: 'checkbox',
    name: '复选框',
    icon: IconCheckbox,
    defaultProps: {
      label: '复选框',
      checked: false
    },
    defaultStyle: {}
  },
  {
    type: 'radio',
    name: '单选框',
    icon: IconCircleDot,
    defaultProps: {
      label: '单选框',
      value: 'option1'
    },
    defaultStyle: {}
  },
  {
    type: 'switch',
    name: '开关',
    icon: IconToggleLeft,
    defaultProps: {
      value: false,
      activeText: '开',
      inactiveText: '关'
    },
    defaultStyle: {}
  },
  {
    type: 'slider',
    name: '滑块',
    icon: IconAdjustments,
    defaultProps: {
      value: 50,
      min: 0,
      max: 100,
      step: 1
    },
    defaultStyle: { width: '200px' }
  },
  {
    type: 'date-picker',
    name: '日期选择器',
    icon: IconCalendar,
    defaultProps: {
      placeholder: '选择日期',
      type: 'date'
    },
    defaultStyle: { width: '200px' }
  },
  {
    type: 'time-picker',
    name: '时间选择器',
    icon: IconClock,
    defaultProps: {
      placeholder: '选择时间'
    },
    defaultStyle: { width: '200px' }
  },
  {
    type: 'rate',
    name: '评分',
    icon: IconStar,
    defaultProps: {
      value: 0,
      max: 5,
      allowHalf: true
    },
    defaultStyle: {}
  },
  {
    type: 'color-picker',
    name: '颜色选择器',
    icon: IconPalette,
    defaultProps: {
      value: '#409EFF'
    },
    defaultStyle: {}
  },
  {
    type: 'card',
    name: '卡片',
    icon: IconLayoutCards,
    defaultProps: {
      header: '卡片标题',
      shadow: 'always',
      bodyStyle: {}
    },
    defaultStyle: { width: '300px', height: '200px' }
  },
  {
    type: 'carousel',
    name: '走马灯',
    icon: IconPlayerPlay,
    defaultProps: {
      height: '150px',
      trigger: 'hover',
      autoplay: true,
      interval: 4000,
      indicator: true,
      arrow: 'hover',
      type: '',
      loop: true,
      direction: 'horizontal',
      items: [
        { content: '轮播项 1', color: '#99a9bf' },
        { content: '轮播项 2', color: '#d3dce6' },
        { content: '轮播项 3', color: '#e5e9f2' }
      ]
    },
    defaultStyle: { width: '400px', height: '200px' }
  },
  {
    type: 'tag',
    name: '标签',
    icon: IconTag,
    defaultProps: {
      text: '标签',
      type: '',
      closable: false,
      disableTransitions: false,
      hit: false,
      color: '',
      size: 'default',
      effect: 'light',
      round: false
    },
    defaultStyle: {}
  },
  {
    type: 'tabs',
    name: '标签页',
    icon: IconTableRow,
    defaultProps: {
      type: '',
      closable: false,
      addable: false,
      editable: false,
      tabPosition: 'top',
      stretch: false,
      beforeLeave: null,
      tabs: [
        { name: 'first', label: '用户管理', content: '用户管理内容' },
        { name: 'second', label: '配置管理', content: '配置管理内容' },
        { name: 'third', label: '角色管理', content: '角色管理内容' }
      ],
      activeTab: 'first'
    },
    defaultStyle: { width: '400px', height: '300px' }
  },
  {
    type: 'table',
    name: '表格',
    icon: IconTable,
    defaultProps: {
      data: [
        { date: '2016-05-02', name: '王小虎', address: '上海市普陀区金沙江路 1518 弄' },
        { date: '2016-05-04', name: '王小虎', address: '上海市普陀区金沙江路 1517 弄' },
        { date: '2016-05-01', name: '王小虎', address: '上海市普陀区金沙江路 1519 弄' }
      ],
      columns: [
        { prop: 'date', label: '日期', width: '180' },
        { prop: 'name', label: '姓名', width: '180' },
        { prop: 'address', label: '地址' }
      ],
      stripe: false,
      border: false,
      size: 'default',
      fit: true,
      showHeader: true,
      highlightCurrentRow: false
    },
    defaultStyle: { width: '600px', height: '300px' }
  }
]

const handleDragStart = (event: DragEvent, component: ComponentInfo) => {
  // 解析 defaultStyle.width 作为初始宽度
  let width = 100
  if (component.defaultStyle && component.defaultStyle.width) {
    const w = component.defaultStyle.width
    if (typeof w === 'string' && w.endsWith('px')) {
      width = parseInt(w)
    } else if (!isNaN(Number(w))) {
      width = Number(w)
    }
  }
  // 移除 style.width，避免和数据 width 冲突
  const style = { ...component.defaultStyle }
  if ('width' in style) delete style.width
  const dragData = {
    type: component.type,
    name: component.name,
    props: { ...component.defaultProps },
    style,
    x: 0,
    y: 0,
    width,
    height: 32
  }
  
  event.dataTransfer?.setData('application/json', JSON.stringify(dragData))
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'copy'
  }
}
</script>

<style scoped>
.component-library {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.library-tabs {
  height: 100%;
}

.library-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow-y: auto;
  padding: 10px;
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 92px;
  height: 72px;
  padding: 6px 0 4px 0;
  border: 1.5px solid var(--el-border-color-lighter);
  border-radius: 8px;
  background-color: var(--el-bg-color);
  cursor: pointer;
  transition: all 0.18s cubic-bezier(.4,0,.2,1);
  user-select: none;
  box-sizing: border-box;
  gap: 2px;
}

.component-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.08);
}

.component-icon {
  font-size: 18px;
  margin-bottom: 2px;
  color: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.component-name {
  font-size: 12px;
  color: var(--el-text-color-regular);
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.5px;
  padding: 0 2px;
  line-height: 1.5;
}
</style>